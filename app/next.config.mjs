import { withSentryConfig } from "@sentry/nextjs";
import bundleAnalyzer from "@next/bundle-analyzer";
import { createMDX } from "fumadocs-mdx/next";

// Load programatically-controlled config options.
import fs from "fs";
const config_options = JSON.parse(
  fs.readFileSync("./next_config_options.json", "utf8"),
);

/** @type {import('next').NextConfig} */
let nextConfig = {
  reactStrictMode: true,
  poweredByHeader: false,
  devIndicators: false, // comment out for nextjs clippy

  // SEO and Performance optimizations
  compress: true,

  images: {
    // hack to force fumadocs not to over optimize images
    deviceSizes: [1200, 1920, 2048, 3840],
  },

  // Headers for SEO and performance
  async headers() {
    if (process.env.NODE_ENV !== "production") {
      return [];
    }

    return [
      {
        source: "/sitemap.xml",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=86400, stale-while-revalidate=43200",
          },
        ],
      },
      {
        source: "/robots.txt",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=86400, stale-while-revalidate=43200",
          },
        ],
      },
    ];
  },
  webpack: (config) => {
    // TODO: find a better way to fix this that doesn't involve a custom webpack config
    // Fixes warning "Critical dependency: the request of a dependency is an expression"
    config.module = {
      ...config.module,
      exprContextCritical: false,
    };
    return config;
  },
  experimental: {
    externalMiddlewareRewritesResolve: true,
    // esmExternals: "loose",
  },
  serverExternalPackages: [
    "@sentry/nextjs",
    "@duckdb/duckdb-wasm",
    "@typescript/vfs",
  ],
  transpilePackages: ["next-auth"],
  redirects: async () => {
    return [
      {
        source: "/:path((?!api/).*)",
        destination: "https://www.braintrust.dev/:path*",
        permanent: true,
        has: [
          {
            type: "host",
            value: "www.braintrustdata.com",
          },
        ],
      },
      {
        // Redirects all paths ending in .md to the same path without the .md
        source: "/docs/:path*.md",
        destination: "/docs/:path*",
        permanent: true,
      },
      {
        // Redirects raw mdx paths
        source: "/docs/:path*.mdx",
        destination: "/docs/llms.mdx/:path*",
        permanent: true,
      },
      {
        source: "/app/:org/settings",
        destination: "/app/:org/settings/team",
        permanent: false,
      },
      {
        source: "/careers/:path",
        destination: "/careers",
        permanent: false,
      },
      {
        source: "/docs/examples",
        destination: "/docs/cookbook",
        permanent: true,
      },
      {
        source: "/docs/examples/:path*",
        destination: "/docs/cookbook",
        permanent: true,
      },
      {
        source: "/docs/cookbook/:path",
        destination: "/docs/cookbook/recipes/:path",
        permanent: true,
      },
      {
        source: "/docs",
        destination: "/docs/start",
        permanent: true,
      },
      {
        source: "/docs/welcome/start",
        destination: "/docs/start/eval-ui",
        permanent: true,
      },
      {
        source: "/docs/welcome/:path*",
        destination: "/docs/start/:path*",
        permanent: true,
      },
      {
        source: "/docs/start/start",
        destination: "/docs/start",
        permanent: true,
      },
      {
        source: "/docs/intro",
        destination: "/docs/start",
        permanent: true,
      },
      {
        source: "/docs/libs/:path*",
        destination: "/docs/reference/libs/:path*",
        permanent: true,
      },
      {
        source: "/docs/api/spec",
        destination: "/docs/reference/api",
        permanent: true,
      },
      {
        source: "/docs/api/walkthrough",
        destination: "/docs/guides/api",
        permanent: true,
      },
      {
        source: "/docs/autoevals/:path*",
        destination: "/docs/reference/autoevals/:path*",
        permanent: true,
      },
      {
        source: "/docs/platform/:path*",
        destination: "/docs/reference/platform/:path*",
        permanent: true,
      },
      {
        source: "/docs/release-notes",
        destination: "/docs/changelog",
        permanent: true,
      },
      {
        source: "/docs/reference/release-notes",
        destination: "/docs/changelog",
        permanent: true,
      },
      {
        source: "/docs/reference/changelog",
        destination: "/docs/changelog",
        permanent: true,
      },
      {
        source: "/docs/self-hosting/:path*",
        destination: "/docs/guides/self-hosting/:path*",
        permanent: true,
      },
      {
        source: "/docs/guides/logging/:path*",
        destination: "/docs/guides/logs/:path*",
        permanent: true,
      },
      {
        source: "/docs/guides/tracing/:path*",
        destination: "/docs/guides/traces/:path*",
        permanent: true,
      },
      {
        source: "/docs/guides/prompts/:path*",
        destination: "/docs/guides/functions/prompts/:path*",
        permanent: true,
      },
      {
        source: "/docs/start/concepts",
        destination: "/docs/start",
        permanent: true,
      },
      {
        source: "/docs/guides/evals/:path*",
        destination: "/docs/guides/experiments/:path*",
        permanent: true,
      },
      {
        source: "/app/:org/p/:project/d",
        destination: "/app/:org/p/:project/datasets",
        permanent: true,
      },
      {
        source: "/app/:org/p/:project/configuration",
        destination: "/app/:org/p/:project/configuration/tags",
        permanent: true,
      },
      {
        source: "/app/:org/p/:project/d/:dataset",
        destination: "/app/:org/p/:project/datasets/:dataset",
        permanent: true,
      },
      {
        source: "/app/:org/p/:project",
        has: [
          {
            type: "query",
            key: "t",
            value: "Log",
          },
        ],
        permanent: false,
        destination: "/app/:org/p/:project/logs",
      },
      {
        source: "/app/:org/p/:project",
        has: [
          {
            type: "query",
            key: "t",
            value: "Log",
          },
        ],
        permanent: false,
        destination: "/app/:org/p/:project/logs",
      },
      {
        source: "/app/:org/p/:project",
        has: [
          {
            type: "query",
            key: "t",
            value: "Experiments",
          },
        ],
        permanent: false,
        destination: "/app/:org/p/:project/experiments",
      },
      {
        source: "/app/:org/p/:project",
        has: [
          {
            type: "query",
            key: "t",
            value: "Datasets",
          },
        ],
        permanent: false,
        destination: "/app/:org/p/:project/datasets",
      },
      {
        source: "/app/:org/p/:project",
        has: [
          {
            type: "query",
            key: "t",
            value: "Configuration",
          },
        ],
        permanent: false,
        destination: "/app/:org/p/:project/configuration",
      },
      {
        source: "/app/:org/p/:project/monitor",
        destination: "/app/:org/monitor?project=:project",
        permanent: false,
      },
      {
        source: "/llms.txt",
        destination: "/docs/llms.txt",
        permanent: true,
      },
    ];
  },
};

if (config_options.output_standalone) {
  nextConfig.output = "standalone";
}

nextConfig = createMDX()(nextConfig);

if (process.env.NODE_ENV === "production") {
  // This entire block is in service of uploading source maps to Sentry
  nextConfig = withSentryConfig(nextConfig, {
    // Suppresses source map uploading logs during build
    silent: true,

    org: "braintrust-ok",
    project: "javascript-nextjs",
    authToken: process.env.SENTRY_AUTH_TOKEN,
    // Upload a larger set of source maps for prettier stack traces (increases build time)
    widenClientFileUpload: true,

    // Transpiles SDK to be compatible with IE11 (increases bundle size)
    // transpileClientSDK: false,

    // Hides source maps from generated client bundles
    hideSourceMaps: true,

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,

    // Capture React component names to see which component a user clicked on in Sentry features like Session Replay
    reactComponentAnnotation: {
      enabled: true,
    },

    // Disable automatic server-side instrumentation
    // All server-side events are sent manually via captureSentryServerMessage and captureSentryServerException
    autoInstrumentServerFunctions: false,
    autoInstrumentMiddleware: false,
    autoInstrumentAppDirectory: false,
  });
}

if (process.env.ANALYZE === "true") {
  const withBundleAnalyzer = bundleAnalyzer();
  nextConfig = withBundleAnalyzer(nextConfig);
}

export default nextConfig;
