import { useFullscreenMonitorCardState as useFullscreenMonitorCardState } from "#/ui/query-parameters";
import { useMemo, useState } from "react";
import { type ChartTimeFrame } from "./time-controls/time-range";
import { MONITOR_CARDS } from "./card-config/monitor-cards.constants";
import { EmptyMonitorState } from "./empty-monitor-state";
import { cn } from "#/utils/classnames";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { MonitorCard } from "./card/monitor-card";
import { useHotkeys } from "react-hotkeys-hook";

export interface CardState {
  isLoading: boolean;
  hasData: boolean;
  hasError: boolean;
  error?: Error;
}

function MonitorCards({
  chartTimeFrame,
  timeBucket,
  projectIds,
  groupBy,
  from,
  experimentIds,
  onBrush,
  isLoadingFromIds,
  tzUTC,
}: {
  chartTimeFrame: ChartTimeFrame;
  timeBucket: "minute" | "hour" | "day";
  projectIds: string[];
  groupBy?: string;
  from: "project_logs" | "experiment";
  experimentIds: string[];
  onBrush: (v: [number, number] | null) => void;
  isLoadingFromIds?: boolean;
  tzUTC?: boolean;
}) {
  const [fullscreenCardState, setFullscreenCardState] =
    useFullscreenMonitorCardState();
  const [cardStates, setCardStates] = useState<CardState[]>([]);

  useHotkeys(
    ["Escape"],
    () => {
      setFullscreenCardState(null);
    },
    [setFullscreenCardState],
    { description: "Exit fullscreen", enabled: Boolean(fullscreenCardState) },
  );

  const commonProps = useMemo(() => {
    return {
      projectIds,
      experimentIds,
      timeBucket,
      chartTimeFrame,
      groupBy,
      from,
      tzUTC,
      onBrush,
    };
  }, [
    timeBucket,
    projectIds,
    experimentIds,
    chartTimeFrame,
    groupBy,
    from,
    tzUTC,
    onBrush,
  ]);

  const allLoadedNoData = useMemo(() => {
    return (
      isLoadingFromIds &&
      cardStates.length &&
      cardStates.every((c) => !c.isLoading && !c.hasData)
    );
  }, [cardStates, isLoadingFromIds]);

  const monitorCards = useMemo(
    () =>
      MONITOR_CARDS.map((config, i) => {
        const setCardState = (state: CardState) => {
          setCardStates((p) => {
            const copy = [...p];
            copy[i] = state;
            return copy;
          });
        };
        return {
          component: (
            <MonitorCard
              key={config.name}
              {...commonProps}
              cardConfig={config}
              setCardState={setCardState}
            />
          ),
          config,
        };
      }),
    [commonProps],
  );

  const hasToolsData = useMemo(() => {
    return cardStates.some((c, i) => {
      if (!monitorCards[i]) {
        return false;
      }
      return Boolean(monitorCards[i].config.toolMetric) && c.hasData;
    });
  }, [cardStates, monitorCards]);

  const numRows = Math.ceil(MONITOR_CARDS.length / 2);

  const chartContainerClassName = "flex h-72 md:h-96 md:flex-1 flex-col";
  const fullscreenClassName = "flex-1 h-auto md:h-auto p-2";
  const rows = useMemo(() => {
    return new Array(numRows).fill(0).map((_, i) => {
      const firstCard = monitorCards[2 * i];
      const secondCard = monitorCards[2 * i + 1];

      // if both are tools and no tools data, hide row
      const firstIsTool = Boolean(firstCard?.config.toolMetric);
      const secondIsTool = Boolean(secondCard?.config.toolMetric);
      const allTools =
        (!firstCard || firstIsTool) && (!secondCard || secondIsTool);
      const hideRow = !hasToolsData && allTools;

      const isFirstFullscreen = firstCard?.config.name === fullscreenCardState;
      const isSecondFullscreen =
        secondCard?.config.name === fullscreenCardState;
      const hasAFullscreen = isFirstFullscreen || isSecondFullscreen;

      return (
        <div
          className={cn("flex flex-none flex-col gap-4 md:flex-row", {
            hidden: hideRow,
            "flex-1 flex-row": hasAFullscreen,
          })}
          key={`monitor-row-${i}`}
        >
          <div
            className={cn(chartContainerClassName, {
              [fullscreenClassName]: isFirstFullscreen,
              hidden: fullscreenCardState && !isFirstFullscreen,
            })}
          >
            {firstCard?.component ?? ""}
          </div>
          <div
            className={cn(chartContainerClassName, {
              [fullscreenClassName]: isSecondFullscreen,
              hidden: fullscreenCardState && !isSecondFullscreen,
            })}
          >
            {secondCard?.component ?? ""}
          </div>
        </div>
      );
    });
  }, [monitorCards, numRows, hasToolsData, fullscreenCardState]);

  const unavailable = useMemo(() => {
    return cardStates.every(
      (c) => c.error && c.error.message.includes("too costly"),
    );
  }, [cardStates]);

  return (
    <>
      {allLoadedNoData && (
        <EmptyMonitorState
          rowType={from === "experiment" ? "experiment" : "logs"}
        />
      )}
      {unavailable && (
        <TableEmptyState label="The monitor page is currently unavailable when running a self-hosted setup with Postgres-only" />
      )}
      <div
        className={cn("flex flex-1 flex-col gap-4", {
          "gap-0": fullscreenCardState,
        })}
      >
        {rows}
      </div>
    </>
  );
}

export { MonitorCards };
